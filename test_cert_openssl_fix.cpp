#include <iostream>
#include <string>
#include <memory>

// Test program to verify OpenSSL compatibility fixes for Android
#include "src/p2psocket/cert.h"

using namespace datatunnel;

int main() {
    std::cout << "OpenSSL Compatibility Fix Test for Android" << std::endl;
    std::cout << "===========================================" << std::endl;
    std::cout << std::endl;
    
    try {
        // Test certificate generation
        std::cout << "Testing certificate generation..." << std::endl;
        
        std::unique_ptr<Cert> cert(new Cert());
        if (!cert) {
            std::cout << "ERROR: Failed to create Cert object" << std::endl;
            return 1;
        }
        
        // Test getting certificate strings
        const std::string& x509_str = cert->GetX509Str();
        const std::string& pkey_str = cert->GetPkeyStr();
        
        if (x509_str.empty()) {
            std::cout << "ERROR: X509 certificate string is empty" << std::endl;
            return 1;
        }
        
        if (pkey_str.empty()) {
            std::cout << "ERROR: Private key string is empty" << std::endl;
            return 1;
        }
        
        std::cout << "SUCCESS: Certificate generated successfully" << std::endl;
        std::cout << "X509 certificate length: " << x509_str.length() << " bytes" << std::endl;
        std::cout << "Private key length: " << pkey_str.length() << " bytes" << std::endl;
        
        // Test fingerprint calculation
        std::cout << std::endl << "Testing fingerprint calculation..." << std::endl;
        const std::string& fingerprint = cert->GetLocalX509CertFingerprint();
        
        if (fingerprint.empty()) {
            std::cout << "ERROR: Certificate fingerprint is empty" << std::endl;
            return 1;
        }
        
        std::cout << "SUCCESS: Fingerprint calculated successfully" << std::endl;
        std::cout << "Fingerprint: " << fingerprint << std::endl;
        
        // Test X509 parsing
        std::cout << std::endl << "Testing X509 parsing..." << std::endl;
        X509* x509 = Cert::stringToX509(x509_str);
        if (!x509) {
            std::cout << "ERROR: Failed to parse X509 certificate from string" << std::endl;
            return 1;
        }
        
        // Test fingerprint calculation from parsed X509
        std::string calculated_fp = Cert::CalculateX509CustomFingerprint(x509);
        X509_free(x509);
        
        if (calculated_fp.empty()) {
            std::cout << "ERROR: Failed to calculate fingerprint from parsed X509" << std::endl;
            return 1;
        }
        
        if (calculated_fp != fingerprint) {
            std::cout << "ERROR: Fingerprint mismatch between methods" << std::endl;
            std::cout << "Original: " << fingerprint << std::endl;
            std::cout << "Calculated: " << calculated_fp << std::endl;
            return 1;
        }
        
        std::cout << "SUCCESS: X509 parsing and fingerprint verification passed" << std::endl;
        
        std::cout << std::endl << "All tests passed successfully!" << std::endl;
        std::cout << std::endl;
        
        std::cout << "Changes Made:" << std::endl;
        std::cout << "1. Added OpenSSL version-specific conditional compilation" << std::endl;
        std::cout << "2. Fixed X509_get_notBefore/notAfter deprecated functions" << std::endl;
        std::cout << "3. Added OpenSSL 3.0+ EVP-based RSA key generation" << std::endl;
        std::cout << "4. Added proper OpenSSL includes for compatibility" << std::endl;
        std::cout << "5. Maintained backward compatibility with OpenSSL 1.x" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "ERROR: Exception caught: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "ERROR: Unknown exception caught" << std::endl;
        return 1;
    }
}
