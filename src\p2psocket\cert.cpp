// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#include "cert.h"

#include <algorithm>
#include <cstdint>
#include <cstdlib>
#include <iostream>
#include <memory>

#include "lenlog.h"

// Include additional OpenSSL headers for compatibility
#include <openssl/opensslv.h>
#if OPENSSL_VERSION_NUMBER >= 0x30000000L
#include <openssl/core_names.h>
#include <openssl/param_build.h>
#endif
//#pragma comment(lib,"libcrypto.lib")
namespace datatunnel {
std::mutex Cert::_muFp;
std::map<std::string, std::string> Cert::_peerFingerPrintsMap;
static const std::string LOG_TAG = "Datachannel::Cert";

Cert::Cert() {
  Cert::GenerateLocalX509Cert();
}
Cert::~Cert() {}
const std::string& Cert::GetLocalX509CertFingerprint() {
  X509* x509 = stringToX509(x509_str);
  x509_fp = Cert::CalculateX509CustomFingerprint(x509);
  std::string debug_str = "Fingerprint (hex):";
  for (char c : x509_fp) {
    char hex[4];
    snprintf(hex, sizeof(hex), " %02x", static_cast<unsigned char>(c));
    debug_str += hex;
  }
  LLog::Log(LOG_INFO, " Datachannel::Cert %s", debug_str.c_str());
  LLog::Log(
      LOG_INFO, " Datachannel::Cert %s",
      std::string("GetLocalX509CertFingerprint2, fp=").append(x509_fp).c_str());
  if (x509)
    X509_free(x509);
  return x509_fp;
}

X509* Cert::stringToX509(const std::string& certString) {
  BIO* bio = BIO_new_mem_buf(certString.c_str(), certString.length());
  if (bio == nullptr) {
    return nullptr;
  }

  X509* cert = PEM_read_bio_X509(bio, nullptr, nullptr, nullptr);
  BIO_free(bio);
  return cert;
}

std::string Cert::evpPkeyToString(EVP_PKEY* pkey) {
  BIO* bio = BIO_new(BIO_s_mem());
  if (bio == nullptr) {
    LLog::Log(LOG_ERROR, " Error creating BIO.");
    return "";
  }

  if (PEM_write_bio_PrivateKey(bio, pkey, nullptr, nullptr, 0, nullptr,
                               nullptr) != 1) {
    LLog::Log(LOG_ERROR, " Error writing EVP_PKEY to BIO.");
    BIO_free(bio);
    return "";
  }

  char* buffer = nullptr;
  long length = BIO_get_mem_data(bio, &buffer);
  std::string result(buffer, length);

  BIO_free(bio);

  return result;
}

std::string Cert::x509ToString(X509* x509) {
  if (x509 == nullptr) {
    LLog::Log(LOG_ERROR, " Datachannel::Cert x509ToString x509 is nullptr");
    return "";
  }
  BIO* bio = BIO_new(BIO_s_mem());
  if (bio == nullptr) {
    LLog::Log(LOG_ERROR, " Error creating BIO.");
    return "";
  }

  if (PEM_write_bio_X509(bio, x509) != 1) {
    LLog::Log(LOG_ERROR, " Error writing X509 to BIO.");
    BIO_free(bio);
    return "";
  }

  char* buffer = nullptr;
  long length = BIO_get_mem_data(bio, &buffer);
  std::string result(buffer, length);

  BIO_free(bio);

  return result;
}

uint32_t Cert::GenerateLocalX509Cert() {
  LLog::Log(LOG_INFO, " Datachannel::Cert GenerateLocalX509Cert");
  const char* const FUNC_NAME = "GenerateLocalX509Cert";
  int ERROR_CODE = -1;
  EVP_PKEY* p_key = nullptr;
  X509* x509 = nullptr;
  uint32_t ret = 0;

  ret = GenerateRsaKey30_(2048, &p_key);
  if (ret != 0 || p_key == nullptr) {
    LLog::Log(
        LOG_ERROR,
        " Datachannel::Cert %s: Failed to generate RSA key, error code: %u",
        FUNC_NAME, ret);
    ret = ERROR_CODE;
    goto cleanup;
  }
  x509 = X509_new();
  if (x509 == nullptr) {
    LLog::Log(LOG_ERROR,
              " Datachannel::Cert %s: Failed to create new X509 certificate",
              FUNC_NAME);
    ret = ERROR_CODE;
    goto cleanup;
  }
  ret = GenerateX509Cert(p_key, x509);
  if (ret != 0) {
    LLog::Log(LOG_ERROR,
              " Datachannel::Cert %s: Failed to generate X509 certificate, "
              "error code: %u",
              FUNC_NAME, ret);
    ret = ERROR_CODE;
    goto cleanup;
  }
  pkey_str = evpPkeyToString(p_key);
  x509_str = x509ToString(x509);

  if (pkey_str.empty() || x509_str.empty()) {
    LLog::Log(
        LOG_ERROR,
        " Datachannel::Cert %s: Failed to convert key or certificate to string",
        FUNC_NAME);
    ret = ERROR_CODE;
    goto cleanup;
  }
cleanup:
  if (p_key != nullptr) {
    EVP_PKEY_free(p_key);
    p_key = nullptr;
  }
  if (x509 != nullptr) {
    X509_free(x509);
    x509 = nullptr;
  }
  return ret;
}

/**
 * GenerateRsaKey30_.
 * Generate an RSA key pair compatible with both OpenSSL 1.x and 3.0+
 * Uses EVP interface for OpenSSL 3.0+ and RSA interface for older versions
 * @param modulus Key length: 1024,2048,4096, etc.
 * 1024 length is low security and no one uses it anymore, at least 2048 bits
 * @param pkey The data structure defined by openssl to store the key requires
 * the caller to release the pkey
 * @return 0 on success, 1 on failure
 */
uint32_t Cert::GenerateRsaKey30_(int32_t modulus, EVP_PKEY** pkey) {
#if OPENSSL_VERSION_NUMBER >= 0x30000000L
  // OpenSSL 3.0+ implementation using EVP interface
  EVP_PKEY_CTX* ctx = NULL;
  OSSL_PARAM_BLD* param_bld = NULL;
  OSSL_PARAM* params = NULL;
  BIGNUM* pub_exp = NULL;

  *pkey = NULL;

  // Create parameter builder
  param_bld = OSSL_PARAM_BLD_new();
  if (!param_bld) {
    goto cleanup;
  }

  // Set public exponent (65537 = RSA_F4)
  pub_exp = BN_new();
  if (!pub_exp || !BN_set_word(pub_exp, RSA_F4)) {
    goto cleanup;
  }

  // Add parameters
  if (!OSSL_PARAM_BLD_push_uint(param_bld, OSSL_PKEY_PARAM_RSA_BITS, modulus) ||
      !OSSL_PARAM_BLD_push_BN(param_bld, OSSL_PKEY_PARAM_RSA_E, pub_exp)) {
    goto cleanup;
  }

  // Build parameters
  params = OSSL_PARAM_BLD_to_param(param_bld);
  if (!params) {
    goto cleanup;
  }

  // Create context and generate key
  ctx = EVP_PKEY_CTX_new_from_name(NULL, "RSA", NULL);
  if (!ctx) {
    goto cleanup;
  }

  if (EVP_PKEY_keygen_init(ctx) <= 0) {
    goto cleanup;
  }

  if (EVP_PKEY_CTX_set_params(ctx, params) <= 0) {
    goto cleanup;
  }

  if (EVP_PKEY_generate(ctx, pkey) <= 0) {
    goto cleanup;
  }

  // Cleanup
  BN_free(pub_exp);
  OSSL_PARAM_free(params);
  OSSL_PARAM_BLD_free(param_bld);
  EVP_PKEY_CTX_free(ctx);
  return 0;

cleanup:
  if (pub_exp) BN_free(pub_exp);
  if (params) OSSL_PARAM_free(params);
  if (param_bld) OSSL_PARAM_BLD_free(param_bld);
  if (ctx) EVP_PKEY_CTX_free(ctx);
  if (*pkey) {
    EVP_PKEY_free(*pkey);
    *pkey = NULL;
  }
  return 1;

#else
  // OpenSSL 1.x implementation using RSA interface
  RSA* rsa = NULL;
  BIGNUM* bne = NULL;
  *pkey = EVP_PKEY_new();
  if (!*pkey) {
    goto cleanup;
  }
  bne = BN_new();
  if (!bne) {
    goto cleanup;
  }

  if (BN_set_word(bne, RSA_F4) != 1) {
    goto cleanup;
  }

  rsa = RSA_new();
  if (!rsa) {
    goto cleanup;
  }

  if (RSA_generate_key_ex(rsa, modulus, bne, NULL) != 1) {
    goto cleanup;
  }

  if (EVP_PKEY_assign_RSA(*pkey, rsa) != 1) {
    goto cleanup;
  }

  rsa = NULL;

  BN_free(bne);
  return 0;

cleanup:
  if (*pkey)
    EVP_PKEY_free(*pkey);
  if (rsa)
    RSA_free(rsa);
  if (bne)
    BN_free(bne);

  return 1;
#endif
}

uint32_t Cert::GenerateX509Cert(EVP_PKEY* pkey, X509* x509) {
  if (pkey == nullptr || x509 == nullptr) {
    LLog::Log(LOG_ERROR,
              " Datachannel::Cert GenerateX509Cert pkey or x509 is nullptr");
    return 1;
  }
  // generate x509
  ASN1_INTEGER_set(X509_get_serialNumber(x509), 1);
  // The X509_gmtime_adj function adds the specified number of seconds to the
  // current time
  // Use X509_getm_* functions for OpenSSL 1.1.0+ compatibility
#if OPENSSL_VERSION_NUMBER >= 0x10100000L
  X509_gmtime_adj(X509_getm_notBefore(x509), 0);
  X509_gmtime_adj(X509_getm_notAfter(x509), 31536000L);
#else
  X509_gmtime_adj(X509_get_notBefore(x509), 0);
  X509_gmtime_adj(X509_get_notAfter(x509), 31536000L);
#endif

  X509_set_pubkey(x509, pkey);
#ifndef _WIN32
  X509_NAME* name;
  name = X509_get_subject_name(x509);

  // country
  X509_NAME_add_entry_by_txt(name, "C", MBSTRING_ASC, (unsigned char*)"CN", -1,
                             -1, 0);
  // organization
  X509_NAME_add_entry_by_txt(name, "O", MBSTRING_ASC, (unsigned char*)"Lenovo",
                             -1, -1, 0);
  // common name
  X509_NAME_add_entry_by_txt(name, "CN", MBSTRING_ASC, (unsigned char*)"Lenovo",
                             -1, -1, 0);
  // origanization unit
  X509_NAME_add_entry_by_txt(name, "OU", MBSTRING_ASC, (unsigned char*)"DC", -1,
                             -1, 0);

  X509_set_issuer_name(x509, name);
#endif
  int ret = X509_sign(x509, pkey, EVP_sha256());
  if (ret == 0) {
    return 1;
  }
  return 0;
}

uint32_t Cert::X509ToDer(X509* x509, uint8_t* buf, uint32_t* size) {
  if (x509 == nullptr || buf == nullptr || size == nullptr) {
    LLog::Log(LOG_ERROR,
              " Datachannel::Cert X509ToDer x509 or buf or size is nullptr");
    return 1;
  }
  int len = i2d_X509(x509, NULL);
  if (len < 0 || (uint32_t)len > *size) {
    return 1;
  }
  uint8_t* ptr = buf;
  *size = i2d_X509(x509, &ptr);
  return 0;
}

std::string Cert::CalculateX509CustomFingerprint(X509* x509) {
  LLog::Log(LOG_INFO, " Datachannel::Cert %s",
            "CalculateX509CustomFingerprint");
  if (x509 == nullptr) {
    LLog::Log(LOG_ERROR, " Datachannel::Cert %s",
              "CalculateX509CustomFingerprint failed, x509 is null");
    return "";
  }
  unsigned char buf[4096];
  unsigned int size = 4096;
  uint32_t ret = X509ToDer(x509, buf, &size);
  if (ret != 0) {
    LLog::Log(
        LOG_INFO, " Datachannel::Cert %s",
        std::string("CalculateX509CustomFingerprint failed, X509ToDer ret:")
            .append(std::to_string(ret))
            .c_str());
    return "";
  }
  uint8_t md[EVP_MAX_MD_SIZE] = {0};
  if (!SHA256(buf, size, md)) {
    LLog::Log(LOG_ERROR, " Datachannel::Cert SHA256 calculation failed");
    return "";
  }
  std::string strfp = ConvertBytesToHexStr(md, SHA256_DIGEST_LENGTH);
  LLog::Log(LOG_INFO, " Datachannel::Cert after ConvertBytesToHexStr %s",
            strfp.c_str());
  if (strfp.length() != SHA256_DIGEST_LENGTH * 2) {
    LLog::Log(LOG_ERROR, " Datachannel::Cert Invalid fingerprint length: %zu",
              strfp.length());
    return "";
  }
  return strfp;
}

std::string Cert::ConvertBytesToHexStr(const unsigned char* bytes,
                                       uint64_t bsize) {
  if (!bytes || bsize == 0 || bsize > (SIZE_MAX - 1) / 2) {
    LLog::Log(LOG_ERROR, " Datachannel::Cert %s",
              "ConvertBytesToHexStr failed");
    return "";
  }
  std::string result;
  result.reserve(bsize * 2);

  char hex[3];
  for (uint64_t i = 0; i < bsize; i++) {
    snprintf(hex, sizeof(hex), "%02x", bytes[i]);
    result += hex;
  }
  for (char c : result) {
    if (!isxdigit(static_cast<unsigned char>(c))) {
      LLog::Log(LOG_ERROR,
                " Datachannel::Cert Invalid hex character generated: 0x%02x",
                static_cast<unsigned char>(c));
      return "";
    }
  }
  return result;
}

void Cert::StrToLower(std::string& str) {
  std::transform(str.begin(), str.end(), str.begin(),
                 [](unsigned char c) { return std::tolower(c); });
}
}  // namespace datatunnel