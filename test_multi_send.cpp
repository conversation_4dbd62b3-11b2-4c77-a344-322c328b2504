#include <iostream>
#include <thread>
#include <chrono>
#include "src/p2psocket/p2psocket.h"

// Simple test to verify multi-send functionality
int main() {
    std::cout << "Testing multi-send functionality..." << std::endl;
    
    // Initialize socket options
    SocketOptions serverOpts = {0};
    serverOpts.mode = MODE_SERVER;
    serverOpts.type = TYPE_QUIC;
    serverOpts.buffer_size = 1024 * 1024; // 1MB buffer
    
    SocketOptions clientOpts = {0};
    clientOpts.mode = MODE_CLIENT;
    clientOpts.type = TYPE_QUIC;
    clientOpts.buffer_size = 1024 * 1024; // 1MB buffer
    
    // Create server socket
    P2P_SOCKET server = QuicCreate(&serverOpts);
    if (server == NULL) {
        std::cout << "Failed to create server socket" << std::endl;
        return -1;
    }
    
    // Set non-direct send mode to test our multi-send implementation
    QuicSetDirectSendMode(server, 0);
    
    // Bind and listen
    if (QuicBind(server, "127.0.0.1", 12345) != 0) {
        std::cout << "Failed to bind server" << std::endl;
        QuicRelease(server);
        return -1;
    }
    
    if (QuicListen(server) != 0) {
        std::cout << "Failed to listen" << std::endl;
        QuicRelease(server);
        return -1;
    }
    
    std::cout << "Server listening on 127.0.0.1:12345" << std::endl;
    
    // Create client socket
    P2P_SOCKET client = QuicCreate(&clientOpts);
    if (client == NULL) {
        std::cout << "Failed to create client socket" << std::endl;
        QuicRelease(server);
        return -1;
    }
    
    // Set non-direct send mode to test our multi-send implementation
    QuicSetDirectSendMode(client, 0);
    
    // Connect to server
    if (QuicConnect(client, "127.0.0.1", 12345) != 0) {
        std::cout << "Failed to connect to server" << std::endl;
        QuicRelease(client);
        QuicRelease(server);
        return -1;
    }
    
    std::cout << "Client connected to server" << std::endl;
    
    // Accept connection on server
    P2P_SOCKET serverConn = QuicAccept(server);
    if (serverConn == NULL) {
        std::cout << "Failed to accept connection" << std::endl;
        QuicRelease(client);
        QuicRelease(server);
        return -1;
    }
    
    // Set non-direct send mode for server connection
    QuicSetDirectSendMode(serverConn, 0);
    
    std::cout << "Connection established" << std::endl;
    
    // Test multiple concurrent sends
    const int numSends = 5;
    const int dataSize = 1024;
    char sendData[dataSize];
    memset(sendData, 'A', dataSize);
    
    std::cout << "Testing " << numSends << " concurrent sends..." << std::endl;
    
    // Send multiple messages rapidly to test multi-send queue
    for (int i = 0; i < numSends; i++) {
        sendData[0] = '0' + i; // Mark each message
        int result = QuicWrite(client, sendData, dataSize);
        if (result < 0) {
            std::cout << "Send " << i << " failed with result: " << result << std::endl;
        } else {
            std::cout << "Send " << i << " queued successfully, bytes: " << result << std::endl;
        }
        
        // Small delay to allow some sends to queue up
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    std::cout << "All sends queued, waiting for completion..." << std::endl;
    
    // Wait a bit for sends to complete
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // Try to receive data on server
    char recvBuffer[dataSize * 2];
    int totalReceived = 0;
    int attempts = 0;
    const int maxAttempts = 10;
    
    while (totalReceived < numSends * dataSize && attempts < maxAttempts) {
        int received = QuicRead(serverConn, recvBuffer, sizeof(recvBuffer));
        if (received > 0) {
            totalReceived += received;
            std::cout << "Received " << received << " bytes, total: " << totalReceived << std::endl;
        } else if (received == 0) {
            std::cout << "No data available, waiting..." << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        } else {
            std::cout << "Receive error: " << received << std::endl;
            break;
        }
        attempts++;
    }
    
    std::cout << "Test completed. Total received: " << totalReceived << " bytes" << std::endl;
    std::cout << "Expected: " << (numSends * dataSize) << " bytes" << std::endl;
    
    // Cleanup
    QuicRelease(serverConn);
    QuicRelease(client);
    QuicRelease(server);
    
    if (totalReceived == numSends * dataSize) {
        std::cout << "SUCCESS: Multi-send test passed!" << std::endl;
        return 0;
    } else {
        std::cout << "FAILURE: Multi-send test failed!" << std::endl;
        return -1;
    }
}
